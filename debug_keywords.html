<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词检测调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .match { background-color: #d4edda; }
        .no-match { background-color: #f8d7da; }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>PDF自动生成关键词检测调试</h1>
    
    <div>
        <h2>测试你的问题</h2>
        <input type="text" id="userInput" placeholder="输入你的问题，例如：总结谷歌2022年的综合性财务情况" value="总结谷歌2022年的综合性财务情况">
        <button onclick="testKeyword()">测试关键词检测</button>
        <div id="result"></div>
    </div>

    <div>
        <h2>预设测试用例</h2>
        <div id="testCases"></div>
    </div>

    <script>
        function shouldGeneratePDF(userPrompt) {
            const keywords = [
                /总结/i,
                /生成.*报告/i,
                /生成.*文档/i,
                /导出.*报告/i,
                /制作.*报告/i,
                /整理.*报告/i,
                /汇总/i,
                /报告/i,
                /文档/i,
                /summary/i,
                /generate.*report/i,
                /create.*report/i,
                /make.*report/i,
                /export.*report/i,
                /document/i,
                /report/i
            ];
            
            const userPromptLower = userPrompt.toLowerCase();
            console.log('Testing prompt:', userPrompt);
            console.log('Lowercase:', userPromptLower);
            
            for (let keyword of keywords) {
                if (keyword.test(userPrompt)) {
                    console.log('Matched keyword:', keyword);
                    return true;
                }
            }
            
            console.log('No keywords matched');
            return false;
        }

        function testKeyword() {
            const input = document.getElementById('userInput').value;
            const result = shouldGeneratePDF(input);
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = `
                <div class="test-case ${result ? 'match' : 'no-match'}">
                    <strong>问题:</strong> ${input}<br>
                    <strong>结果:</strong> ${result ? '✓ 会生成PDF' : '✗ 不会生成PDF'}
                </div>
            `;
        }

        // 预设测试用例
        const testCases = [
            { prompt: "总结谷歌2022年的综合性财务情况", expected: true },
            { prompt: "请帮我总结一下这个项目", expected: true },
            { prompt: "生成一份销售报告", expected: true },
            { prompt: "制作项目汇总", expected: true },
            { prompt: "请生成文档", expected: true },
            { prompt: "Generate a summary report", expected: true },
            { prompt: "Create a document", expected: true },
            { prompt: "今天天气怎么样", expected: false },
            { prompt: "你好，请介绍一下自己", expected: false },
            { prompt: "帮我写一段代码", expected: false },
        ];

        function runAllTests() {
            const container = document.getElementById('testCases');
            container.innerHTML = '';
            
            testCases.forEach((testCase, index) => {
                const result = shouldGeneratePDF(testCase.prompt);
                const isCorrect = result === testCase.expected;
                
                const div = document.createElement('div');
                div.className = `test-case ${isCorrect ? (result ? 'match' : 'no-match') : 'no-match'}`;
                div.innerHTML = `
                    <strong>测试 ${index + 1}:</strong> ${testCase.prompt}<br>
                    <strong>结果:</strong> ${result ? '✓ 会生成PDF' : '✗ 不会生成PDF'}<br>
                    <strong>期望:</strong> ${testCase.expected ? '✓ 应该生成PDF' : '✗ 不应该生成PDF'}<br>
                    <strong>状态:</strong> ${isCorrect ? '✅ 正确' : '❌ 错误'}
                `;
                container.appendChild(div);
            });
        }

        // 页面加载时运行所有测试
        window.onload = function() {
            runAllTests();
            testKeyword(); // 测试默认输入
        };
    </script>
</body>
</html>
